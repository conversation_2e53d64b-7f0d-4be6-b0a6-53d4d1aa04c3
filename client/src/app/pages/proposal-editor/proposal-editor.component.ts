import { Component, OnInit } from '@angular/core';
import { Textarea } from 'primeng/textarea';
import { Button, ButtonDirective } from 'primeng/button';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { Client } from '../../../lib/models';
import { Select, SelectChangeEvent } from 'primeng/select';
import { Dialog } from 'primeng/dialog';
import { InputText } from 'primeng/inputtext';
import { ProgressSpinner } from 'primeng/progressspinner';
import { Calendar } from 'primeng/calendar';
import { ClientService, ProposalService } from '../../services';
import { ConfirmationService, MessageService } from 'primeng/api';
import { NgForOf, NgIf } from '@angular/common';
import { ProposalDraft } from '../../../lib/validators';
import { addDays, format } from 'date-fns';

@Component({
  selector: 'chm-proposal-editor',
  imports: [
    Textarea,
    ButtonDirective,
    FormsModule,
    Button,
    Select,
    Dialog,
    InputText,
    ProgressSpinner,
    Calendar,
    ReactiveFormsModule,
    NgForOf,
    NgIf,
  ],
  templateUrl: './proposal-editor.component.html',
  styleUrl: './proposal-editor.component.css',
  standalone: true,
})
export class ProposalEditorComponent implements OnInit {
  selectedClient?: Client;
  clients: Client[] = [];
  clientDialogVisible = false;
  editMode = false;
  clientForm!: FormGroup;
  generateProposalFormGroup!: FormGroup;
  proposalDraft?: ProposalDraft;
  isGeneratingDraft = false;

  constructor(
    private fb: FormBuilder,
    private clientService: ClientService,
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
    private proposalService: ProposalService,
  ) {
    this.clientForm = this.fb.group({
      name: [null, Validators.required],
      email: [null, [Validators.required, Validators.email]],
      rep_full_name: [null, Validators.required],
    });

    this.generateProposalFormGroup = this.fb.group({
      client_id: [null, Validators.required],
      hourly_rate: [59.77, Validators.required],
      currency: ['BGN', Validators.required],
      expiration_date: [
        format(addDays(new Date(), 14), 'yyyy-MM-dd'),
        Validators.required,
      ],
      prompt: [null, Validators.required],
      current_draft: [null],
    });
  }

  handleClientChange(event: SelectChangeEvent) {
    this.selectedClient = event.value;
  }

  handleGenerateProposalDraft() {
    this.isGeneratingDraft = true;
    this.proposalService
      .createDraft(this.generateProposalFormGroup.value)
      .subscribe({
        next: (proposalDraft) => {
          this.proposalDraft = proposalDraft;
          this.generateProposalFormGroup.patchValue({
            current_draft: proposalDraft,
          });
          this.isGeneratingDraft = false;
        },
        error: (error) => {
          console.error('Error generating proposal draft:', error);
          this.isGeneratingDraft = false;
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to generate proposal draft. Please try again.',
          });
        },
      });
  }

  ngOnInit() {
    this.clientService.getAll().subscribe((clients) => {
      this.clients = clients;
      this.selectedClient = clients[0];
      this.generateProposalFormGroup.patchValue({
        client_id: clients[0]?.id,
      });
    });
  }

  handleCreateClient() {
    if (this.editMode) {
      this.clientService
        .update(this.selectedClient!.id, this.clientForm.value)
        .subscribe(() => {
          this.clients = this.clients.map((c) => {
            if (c.id === this.selectedClient!.id) {
              return {
                ...c,
                ...this.clientForm.value,
              };
            }

            return c;
          });

          this.selectedClient = {
            ...this.selectedClient!,
            ...this.clientForm.value,
          };
          this.clientDialogVisible = false;
          this.clientForm.reset();
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Client edited successfully',
          });

          this.editMode = false;
        });

      return;
    }

    this.clientService.create(this.clientForm.value).subscribe((client) => {
      this.clients = [client, ...this.clients];
      this.selectedClient = client;
      this.clientDialogVisible = false;
      this.clientForm.reset();
      this.messageService.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Client created successfully',
      });
    });
  }

  handleEditClient() {
    if (!this.selectedClient) return;

    this.clientForm.patchValue({
      name: this.selectedClient.name,
      email: this.selectedClient.email,
      rep_full_name: this.selectedClient.rep_full_name,
    });

    this.clientDialogVisible = true;
    this.editMode = true;
  }

  handleDeleteClient() {
    if (!this.selectedClient) return;

    this.confirmationService.confirm({
      message: `Are you sure you want to delete ${this.selectedClient.name}?`,
      accept: () => {
        this.clientService.delete(this.selectedClient!.id).subscribe(() => {
          this.clients = this.clients.filter(
            (c) => c.id !== this.selectedClient!.id,
          );
          this.selectedClient = undefined;
          this.messageService.add({
            severity: 'success',
            summary: 'Deleted',
            detail: 'Client deleted',
          });
        });
      },
    });
  }

  handleCreateProposal() {}
}
