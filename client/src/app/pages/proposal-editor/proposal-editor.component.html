<div
  class="max-w-3xl mx-auto p-6 pt rounded-lg shadow-lg">
  <h2 class="text-2xl font-bold mb-4">AI Prompt Assistant</h2>
  <form [formGroup]="generateProposalFormGroup">
    <div class="mb-4">
      <div class="flex items-center gap-2">
        <p-select
          (onChange)="handleClientChange($event)"
          [options]="clients"
          class="w-full"
          formControlName="client_id"
          inputId="clientDropdown"
          optionLabel="name"
          optionValue="id"
          panelStyleClass="bg-slate-900 text-white"
          placeholder="Choose a client"
        ></p-select>
        <p-button (click)="clientDialogVisible = true" aria-label="Add Client" icon="pi pi-user-plus"
                  severity="success"/>
        <p-button (click)="handleEditClient()" [disabled]="!selectedClient" aria-label="Edit Client" icon="pi pi-pencil"
                  severity="info"/>
        <p-button (click)="handleDeleteClient()" [disabled]="!selectedClient" aria-label="Delete Client"
                  icon="pi pi-trash"
                  severity="danger"/>
      </div>
      <div class="flex items-center gap-4 mt-4">
        <label class="font-semibold w-24 mr-5" for="proposalRate">Hourly Rate</label>
        <input class="flex-auto" formControlName="hourly_rate" id="proposalRate" pInputText placeholder="100.00"
               type="number"/>
      </div>
      <div class="flex items-center gap-4 mt-2 mb-4">
        <label class="font-semibold w-24 mr-5" for="proposalCurrency">Currency</label>
        <input class="flex-auto" formControlName="currency" id="proposalCurrency" pInputText placeholder="USD"/>
      </div>
      <div class="flex items-center gap-4 mt-2 mb-4">
        <label class="font-semibold w-24 mr-5" for="proposalExpiration">Expires at</label>
        <p-calendar
          [iconDisplay]="'input'"
          [inputStyle]="{ width: '100%' }"
          [showButtonBar]="true"
          [showIcon]="true"
          [style]="{ width: '100%' }"
          class="flex-auto"
          dateFormat="yy-mm-dd"
          formControlName="expiration_date"
          inputId="proposalExpiration"
          placeholder="Select expiration date">
        </p-calendar>
      </div>
    </div>
    <textarea
      class="w-full text-white placeholder-pink-200 bg-purple-900 border-purple-700"
      formControlName="prompt"
      pTextarea
      placeholder="Describe the project or goal for the proposal..."
      rows="6"
      style="border-radius: 0.5rem;"
    ></textarea>
    <button
      (click)="handleGenerateProposalDraft()"
      [disabled]="generateProposalFormGroup.invalid || isGeneratingDraft"
      class="mt-2 bg-gradient-to-r from-purple-500 to-pink-500 border-none"
      label="Generate Proposal Draft"
      pButton
      style="border-radius: 0.5rem; padding: 0.75rem 1.5rem;"
    ></button>
  </form>

  <!-- Loading Spinner -->
  <div *ngIf="isGeneratingDraft" class="flex flex-col items-center justify-center mt-4 mb-4">
    <p-progressSpinner
      [style]="{ width: '50px', height: '50px' }"
      animationDuration="1s"
      fill="transparent"
      strokeWidth="4"
      styleClass="custom-spinner">
    </p-progressSpinner>
    <p class="mt-2 text-purple-300 font-medium">Generating your proposal draft...</p>
  </div>
  <div *ngIf="proposalDraft" class="mt-8 space-y-8">
    <!-- Project Overview Section -->
    <div
      class="bg-gradient-to-r from-slate-800 via-slate-700 to-slate-800 rounded-xl p-6 shadow-2xl border border-slate-600">
      <div class="flex items-center gap-3 mb-6">
        <div class="bg-gradient-to-r from-purple-500 to-pink-500 p-2 rounded-lg">
          <i class="pi pi-file-edit text-white text-lg"></i>
        </div>
        <h3 class="text-xl font-bold text-purple-100">Project Overview</h3>
      </div>

      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-purple-200 mb-2">Project Title</label>
          <input [(ngModel)]="proposalDraft.project_title" [ngModelOptions]="{standalone: true}"
                 class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all duration-200"
                 pInputText placeholder="Enter project title"/>
        </div>

        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-purple-200 mb-2">Price</label>
            <input [(ngModel)]="proposalDraft.price" [ngModelOptions]="{standalone: true}"
                   class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all duration-200"
                   pInputText placeholder="0.00" type="number"/>
          </div>
          <div>
            <label class="block text-sm font-medium text-purple-200 mb-2">Currency</label>
            <input [(ngModel)]="proposalDraft.currency" [ngModelOptions]="{standalone: true}"
                   class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all duration-200"
                   pInputText placeholder="USD"/>
          </div>
        </div>
      </div>
    </div>

    <!-- Problems Section -->
    <div
      class="bg-gradient-to-r from-red-900/20 via-red-800/20 to-red-900/20 rounded-xl p-6 shadow-2xl border border-red-500/30 backdrop-blur-sm">
      <div class="flex items-center gap-3 mb-6">
        <div class="bg-gradient-to-r from-red-500 to-red-600 p-2 rounded-lg">
          <i class="pi pi-exclamation-triangle text-white text-lg"></i>
        </div>
        <h3 class="text-xl font-bold text-slate-800">Problems & Challenges</h3>
      </div>

      <div class="space-y-4">
        <div *ngFor="let p of proposalDraft.problems; let i = index"
             class="group bg-slate-800/60 backdrop-blur-sm border border-slate-600/50 rounded-lg p-5 hover:border-red-400/50 transition-all duration-300 hover:shadow-lg hover:shadow-red-500/10 relative">
          <div class="space-y-3">
            <div>
              <label class="block text-sm font-medium text-red-200 mb-2">Problem Title</label>
              <input [(ngModel)]="p.title" [ngModelOptions]="{standalone: true}"
                     class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-red-400 focus:ring-2 focus:ring-red-400/20 transition-all duration-200"
                     pInputText placeholder="Describe the main problem"/>
            </div>
            <div>
              <label class="block text-sm font-medium text-red-200 mb-2">Problem Description</label>
              <textarea [(ngModel)]="p.description" [ngModelOptions]="{standalone: true}"
                        class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-red-400 focus:ring-2 focus:ring-red-400/20 transition-all duration-200 min-h-[100px]"
                        pInputTextarea
                        placeholder="Provide detailed explanation of the problem and its impact"></textarea>
            </div>
          </div>
          <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300">
            <button (click)="proposalDraft.problems.splice(i, 1)"
                    aria-label="Remove Problem"
                    class="w-8 h-8 bg-red-500/80 hover:bg-red-500 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 shadow-lg">
              <i class="pi pi-trash text-xs"></i>
            </button>
          </div>
        </div>
      </div>

      <div class="mt-6 flex justify-center">
        <button (click)="proposalDraft.problems.push({ title: '', description: '' })"
                class="flex items-center gap-2 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-medium px-6 py-3 rounded-xl transition-all duration-200 hover:shadow-lg hover:shadow-red-500/25 hover:-translate-y-0.5">
          <i class="pi pi-plus text-sm"></i>
          <span>Add Problem</span>
        </button>
      </div>
    </div>

    <!-- Solutions Section -->
    <div
      class="bg-gradient-to-r from-green-900/20 via-green-800/20 to-green-900/20 rounded-xl p-6 shadow-2xl border border-green-500/30 backdrop-blur-sm">
      <div class="flex items-center gap-3 mb-6">
        <div class="bg-gradient-to-r from-green-500 to-green-600 p-2 rounded-lg">
          <i class="pi pi-lightbulb text-white text-lg"></i>
        </div>
        <h3 class="text-xl font-bold text-slate-800">Solutions & Approach</h3>
      </div>

      <div class="space-y-4">
        <div *ngFor="let s of proposalDraft.solutions; let i = index"
             class="group bg-slate-800/60 backdrop-blur-sm border border-slate-600/50 rounded-lg p-5 hover:border-green-400/50 transition-all duration-300 hover:shadow-lg hover:shadow-green-500/10 relative">
          <div class="space-y-3">
            <div>
              <label class="block text-sm font-medium text-green-200 mb-2">Solution Title</label>
              <input [(ngModel)]="s.title" [ngModelOptions]="{standalone: true}"
                     class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-green-400 focus:ring-2 focus:ring-green-400/20 transition-all duration-200"
                     pInputText placeholder="Name your solution approach"/>
            </div>
            <div>
              <label class="block text-sm font-medium text-green-200 mb-2">Solution Description</label>
              <textarea [(ngModel)]="s.description" [ngModelOptions]="{standalone: true}"
                        class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-green-400 focus:ring-2 focus:ring-green-400/20 transition-all duration-200 min-h-[100px]"
                        pInputTextarea
                        placeholder="Explain how this solution addresses the problem and its benefits"></textarea>
            </div>
          </div>
          <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300">
            <button (click)="proposalDraft.solutions.splice(i, 1)"
                    aria-label="Remove Solution"
                    class="w-8 h-8 bg-red-500/80 hover:bg-red-500 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 shadow-lg">
              <i class="pi pi-trash text-xs"></i>
            </button>
          </div>
        </div>
      </div>

      <div class="mt-6 flex justify-center">
        <button (click)="proposalDraft.solutions.push({ title: '', description: '' })"
                class="flex items-center gap-2 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-medium px-6 py-3 rounded-xl transition-all duration-200 hover:shadow-lg hover:shadow-green-500/25 hover:-translate-y-0.5">
          <i class="pi pi-plus text-sm"></i>
          <span>Add Solution</span>
        </button>
      </div>
    </div>

    <!-- Scope Section -->
    <div
      class="bg-gradient-to-r from-blue-900/20 via-blue-800/20 to-blue-900/20 rounded-xl p-6 shadow-2xl border border-blue-500/30 backdrop-blur-sm">
      <div class="flex items-center gap-3 mb-6">
        <div class="bg-gradient-to-r from-blue-500 to-blue-600 p-2 rounded-lg">
          <i class="pi pi-list text-white text-lg"></i>
        </div>
        <h3 class="text-xl font-bold text-slate-800">Project Scope</h3>
      </div>

      <div class="space-y-4">
        <div *ngFor="let s of proposalDraft.scope; let i = index"
             class="group bg-slate-800/60 backdrop-blur-sm border border-slate-600/50 rounded-lg p-5 hover:border-blue-400/50 transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/10 relative">
          <div class="flex items-start gap-3">
            <div class="flex-shrink-0 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center mt-1">
              <span class="text-white text-xs font-bold">{{ i + 1 }}</span>
            </div>
            <div class="flex-1">
              <label class="block text-sm font-medium text-blue-200 mb-2">Scope Item</label>
              <textarea [(ngModel)]="proposalDraft.scope[i]" [ngModelOptions]="{standalone: true}"
                        class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-blue-400 focus:ring-2 focus:ring-blue-400/20 transition-all duration-200 min-h-[80px]"
                        pInputTextarea placeholder="Describe what's included in this scope item"></textarea>
            </div>
            <div class="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-all duration-300">
              <button (click)="proposalDraft.scope.splice(i, 1)"
                      aria-label="Remove scope item"
                      class="w-8 h-8 bg-red-500/80 hover:bg-red-500 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 shadow-lg">
                <i class="pi pi-trash text-xs"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="mt-6 flex justify-center">
        <button (click)="proposalDraft.scope.push('')"
                class="flex items-center gap-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium px-6 py-3 rounded-xl transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/25 hover:-translate-y-0.5">
          <i class="pi pi-plus text-sm"></i>
          <span>Add Scope Item</span>
        </button>
      </div>
    </div>

    <!-- Timeline Section -->
    <div
      class="bg-gradient-to-r from-orange-900/20 via-orange-800/20 to-orange-900/20 rounded-xl p-6 shadow-2xl border border-orange-500/30 backdrop-blur-sm">
      <div class="flex items-center gap-3 mb-6">
        <div class="bg-gradient-to-r from-orange-500 to-orange-600 p-2 rounded-lg">
          <i class="pi pi-calendar text-white text-lg"></i>
        </div>
        <h3 class="text-xl font-bold text-slate-800">Project Timeline</h3>
      </div>

      <div class="space-y-4">
        <div *ngFor="let t of proposalDraft.timeline; let i = index"
             class="group bg-slate-800/60 backdrop-blur-sm border border-slate-600/50 rounded-lg p-5 hover:border-orange-400/50 transition-all duration-300 hover:shadow-lg hover:shadow-orange-500/10 relative">
          <div class="flex items-start gap-4">
            <div class="flex-shrink-0 w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
              <i class="pi pi-clock text-white text-sm"></i>
            </div>
            <div class="flex-1 space-y-3">
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-orange-200 mb-2">Phase Title</label>
                  <input [(ngModel)]="t.title" [ngModelOptions]="{standalone: true}"
                         class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-orange-400 focus:ring-2 focus:ring-orange-400/20 transition-all duration-200"
                         pInputText placeholder="Phase or milestone name"/>
                </div>
                <div>
                  <label class="block text-sm font-medium text-orange-200 mb-2">Duration</label>
                  <input [(ngModel)]="t.duration" [ngModelOptions]="{standalone: true}"
                         class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-orange-400 focus:ring-2 focus:ring-orange-400/20 transition-all duration-200"
                         pInputText placeholder="e.g., 2 weeks, 1 month"/>
                </div>
              </div>
              <div>
                <label class="block text-sm font-medium text-orange-200 mb-2">Description</label>
                <textarea [(ngModel)]="t.description" [ngModelOptions]="{standalone: true}"
                          class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-orange-400 focus:ring-2 focus:ring-orange-400/20 transition-all duration-200 min-h-[80px]"
                          pInputTextarea placeholder="Describe what will be accomplished in this phase"></textarea>
              </div>
            </div>
            <div class="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-all duration-300">
              <button (click)="proposalDraft.timeline.splice(i, 1)"
                      aria-label="Remove Timeline Item"
                      class="w-8 h-8 bg-red-500/80 hover:bg-red-500 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 shadow-lg">
                <i class="pi pi-trash text-xs"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="mt-6 flex justify-center">
        <button (click)="proposalDraft.timeline.push({ title: '', duration: '', description: '' })"
                class="flex items-center gap-2 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium px-6 py-3 rounded-xl transition-all duration-200 hover:shadow-lg hover:shadow-orange-500/25 hover:-translate-y-0.5">
          <i class="pi pi-plus text-sm"></i>
          <span>Add Timeline Item</span>
        </button>
      </div>
    </div>

    <!-- Pricing Section -->
    <div
      class="bg-gradient-to-r from-emerald-900/20 via-emerald-800/20 to-emerald-900/20 rounded-xl p-6 shadow-2xl border border-emerald-500/30 backdrop-blur-sm">
      <div class="flex items-center gap-3 mb-6">
        <div class="bg-gradient-to-r from-emerald-500 to-emerald-600 p-2 rounded-lg">
          <i class="pi pi-dollar text-white text-lg"></i>
        </div>
        <h3 class="text-xl font-bold text-slate-800">Pricing Breakdown</h3>
      </div>

      <div class="space-y-4">
        <div *ngFor="let p of proposalDraft.pricing; let i = index"
             class="group bg-slate-800/60 backdrop-blur-sm border border-slate-600/50 rounded-lg p-5 hover:border-emerald-400/50 transition-all duration-300 hover:shadow-lg hover:shadow-emerald-500/10 relative">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-emerald-200 mb-2">Service/Item</label>
              <input [(ngModel)]="p.item" [ngModelOptions]="{standalone: true}"
                     class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-emerald-400 focus:ring-2 focus:ring-emerald-400/20 transition-all duration-200"
                     pInputText placeholder="Service or deliverable name"/>
            </div>
            <div class="grid grid-cols-3 gap-4">
              <div>
                <label class="block text-sm font-medium text-emerald-200 mb-2">Hours</label>
                <input [(ngModel)]="p.hours" [ngModelOptions]="{standalone: true}"
                       class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-emerald-400 focus:ring-2 focus:ring-emerald-400/20 transition-all duration-200"
                       pInputText placeholder="0" type="number"/>
              </div>
              <div>
                <label class="block text-sm font-medium text-emerald-200 mb-2">Rate</label>
                <input [(ngModel)]="p.rate" [ngModelOptions]="{standalone: true}"
                       class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-emerald-400 focus:ring-2 focus:ring-emerald-400/20 transition-all duration-200"
                       pInputText placeholder="0.00" type="number"/>
              </div>
              <div>
                <label class="block text-sm font-medium text-emerald-200 mb-2">Total</label>
                <div class="relative">
                  <input [(ngModel)]="p.total" [ngModelOptions]="{standalone: true}"
                         class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-emerald-400 focus:ring-2 focus:ring-emerald-400/20 transition-all duration-200"
                         pInputText placeholder="0.00" type="number"/>
                  <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                    <i class="pi pi-calculator text-emerald-400 text-sm"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300">
            <button (click)="proposalDraft.pricing.splice(i, 1)"
                    aria-label="Remove Pricing Item"
                    class="w-8 h-8 bg-red-500/80 hover:bg-red-500 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 shadow-lg">
              <i class="pi pi-trash text-xs"></i>
            </button>
          </div>
        </div>
      </div>

      <div class="mt-6 flex justify-center">
        <button (click)="proposalDraft.pricing.push({ item: '', hours: 0, rate: 0, total: 0 })"
                class="flex items-center gap-2 bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white font-medium px-6 py-3 rounded-xl transition-all duration-200 hover:shadow-lg hover:shadow-emerald-500/25 hover:-translate-y-0.5">
          <i class="pi pi-plus text-sm"></i>
          <span>Add Pricing Item</span>
        </button>
      </div>
    </div>

    <!-- Summary Section -->
    <div
      class="bg-gradient-to-r from-violet-900/20 via-violet-800/20 to-violet-900/20 rounded-xl p-6 shadow-2xl border border-violet-500/30 backdrop-blur-sm">
      <div class="flex items-center gap-3 mb-6">
        <div class="bg-gradient-to-r from-violet-500 to-violet-600 p-2 rounded-lg">
          <i class="pi pi-check-circle text-white text-lg"></i>
        </div>
        <h3 class="text-xl font-bold text-slate-800">Project Summary</h3>
      </div>

      <div class="space-y-4">
        <div *ngFor="let s of proposalDraft.summary; let i = index"
             class="group bg-slate-800/60 backdrop-blur-sm border border-slate-600/50 rounded-lg p-5 hover:border-violet-400/50 transition-all duration-300 hover:shadow-lg hover:shadow-violet-500/10 relative">
          <div class="flex items-start gap-3">
            <div class="flex-shrink-0 w-6 h-6 bg-violet-500 rounded-full flex items-center justify-center mt-1">
              <i class="pi pi-check text-white text-xs"></i>
            </div>
            <div class="flex-1">
              <label class="block text-sm font-medium text-violet-200 mb-2">Summary Point</label>
              <input [(ngModel)]="proposalDraft.summary[i]" [ngModelOptions]="{standalone: true}"
                     class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-violet-400 focus:ring-2 focus:ring-violet-400/20 transition-all duration-200"
                     pInputText placeholder="Key takeaway or benefit"/>
            </div>
            <div class="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-all duration-300">
              <button (click)="proposalDraft.summary.splice(i, 1)"
                      aria-label="Remove Summary Point"
                      class="w-8 h-8 bg-red-500/80 hover:bg-red-500 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 shadow-lg">
                <i class="pi pi-trash text-xs"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="mt-6 flex justify-center">
        <button (click)="proposalDraft.summary.push('')"
                class="flex items-center gap-2 bg-gradient-to-r from-violet-500 to-violet-600 hover:from-violet-600 hover:to-violet-700 text-white font-medium px-6 py-3 rounded-xl transition-all duration-200 hover:shadow-lg hover:shadow-violet-500/25 hover:-translate-y-0.5">
          <i class="pi pi-plus text-sm"></i>
          <span>Add Summary Point</span>
        </button>
      </div>
    </div>
  </div>
  <div class="flex justify-center mt-6">
    <p-button
      (click)="handleCreateProposal()"
      *ngIf="proposalDraft"
      class="bg-gradient-to-r rounded-lg from-purple-600 to-pink-500 text-white font-semibold"
      label="Create Proposal"
    ></p-button>
  </div>
</div>


<p-dialog [(visible)]="clientDialogVisible" [breakpoints]="{ '1199px': '75vw', '575px': '90vw' }"
          [draggable]="false"
          [header]="editMode ? 'Edit Client' : 'Create Client'" [modal]="true" [resizable]="false"
          [style]="{ width: '50vw' }">
  <span class="p-text-secondary block mb-6">Enter details for the client.</span>

  <form [formGroup]="clientForm">
    <div class="flex items-center gap-4 mb-4">
      <label class="font-semibold w-24 mr-5" for="clientName">Name</label>
      <input class="flex-auto" formControlName="name" id="clientName" pInputText placeholder="Acme Corp"/>
    </div>
    <div class="flex items-center gap-4 mb-4">
      <label class="font-semibold w-24 mr-5" for="clientEmail">Email</label>
      <input class="flex-auto" formControlName="email" id="clientEmail" pInputText placeholder="<EMAIL>"/>
    </div>
    <div class="flex items-center gap-4 mb-6">
      <label class="font-semibold w-24 mr-5" for="clientRep">Representative</label>
      <input class="flex-auto" formControlName="rep_full_name" id="clientRep" pInputText placeholder="Jane Doe"/>
    </div>

    <div class="flex justify-end gap-2">
      <p-button (click)="clientDialogVisible = false" label="Cancel" severity="secondary"/>
      <p-button (click)="handleCreateClient()" [disabled]="clientForm.invalid" [label]="editMode ? 'Save' : 'Create'"/>
    </div>
  </form>
</p-dialog>
