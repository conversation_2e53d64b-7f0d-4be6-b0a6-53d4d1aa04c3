h1,
h2 {
  font-family: "Syne";
}

/* Custom spinner styling to match the purple theme */
::ng-deep .custom-spinner .p-progress-spinner-circle {
  stroke: #a855f7; /* Purple-500 color to match the button gradient */
  animation: p-progress-spinner-dash 1.5s ease-in-out infinite;
}

::ng-deep .custom-spinner .p-progress-spinner-svg {
  animation: p-progress-spinner-rotate 2s linear infinite;
}

/* Enhanced section animations and effects */
.group:hover {
  transform: translateY(-2px);
}

/* Custom focus states for inputs */
::ng-deep .p-inputtext:focus,
::ng-deep .p-inputtextarea:focus {
  box-shadow: 0 0 0 2px rgba(168, 85, 247, 0.2);
  border-color: #a855f7 !important;
}

/* Smooth transitions for all interactive elements */
* {
  transition: all 0.2s ease-in-out;
}

/* Enhanced button hover effects */
::ng-deep .p-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Section header icon animations */
.bg-gradient-to-r:hover {
  transform: scale(1.05);
}

/* Backdrop blur enhancement for better visual depth */
.backdrop-blur-sm {
  backdrop-filter: blur(8px);
}

/* Custom scrollbar for textareas */
::ng-deep .p-inputtextarea::-webkit-scrollbar {
  width: 6px;
}

::ng-deep .p-inputtextarea::-webkit-scrollbar-track {
  background: rgba(71, 85, 105, 0.3);
  border-radius: 3px;
}

::ng-deep .p-inputtextarea::-webkit-scrollbar-thumb {
  background: rgba(168, 85, 247, 0.5);
  border-radius: 3px;
}

::ng-deep .p-inputtextarea::-webkit-scrollbar-thumb:hover {
  background: rgba(168, 85, 247, 0.7);
}
