import { Component, OnInit } from '@angular/core';
import { Table } from 'primeng/table';
import { Button } from 'primeng/button';
import { Tag } from 'primeng/tag';
import { ConfirmDialog } from 'primeng/confirmdialog';
import { Toast } from 'primeng/toast';
import { Toolbar } from 'primeng/toolbar';
import { InputText } from 'primeng/inputtext';
import { Calendar } from 'primeng/calendar';
import { Dropdown } from 'primeng/dropdown';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';

import { Proposal } from '../../../lib/models';
import { ProposalService } from '../../services';
import { ConfirmationService, MessageService } from 'primeng/api';

@Component({
  selector: 'chm-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    Table,
    Button,
    Tag,
    ConfirmDialog,
    Toast,
    Toolbar,
    InputText,
    Calendar,
    Dropdown,
  ],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.css',
})
export class DashboardComponent implements OnInit {
  proposals: Proposal[] = [];
  loading = true;
  searchValue = '';
  selectedProposals: Proposal[] = [];

  statusOptions = [
    { label: 'All Statuses', value: null },
    { label: 'Draft', value: 'draft' },
    { label: 'Sent', value: 'sent' },
    { label: 'Accepted', value: 'accepted' },
    { label: 'Partially Paid', value: 'partially_paid' },
    { label: 'Paid', value: 'paid' },
  ];

  selectedStatus: any = null;

  constructor(
    private proposalService: ProposalService,
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
    private router: Router
  ) {}

  ngOnInit() {
    this.loadProposals();
  }
