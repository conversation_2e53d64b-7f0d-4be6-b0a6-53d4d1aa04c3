<div class="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 p-6">
  <!-- Header Section -->
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-4xl font-bold text-white mb-2">Proposals Dashboard</h1>
        <p class="text-slate-400 text-lg">Manage and track all your business proposals</p>
      </div>
      <div class="flex items-center gap-4">
        <div class="bg-slate-800/50 backdrop-blur-sm border border-slate-600 rounded-lg px-4 py-2">
          <span class="text-slate-300 text-sm">Total Proposals: </span>
          <span class="text-white font-bold text-lg">{{ proposals.length }}</span>
        </div>
        <button
          (click)="createNewProposal()"
          class="flex items-center gap-2 bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-semibold px-6 py-3 rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-purple-500/25">
          <i class="pi pi-plus text-sm"></i>
          <span>New Proposal</span>
        </button>
      </div>
    </div>
  </div>

  <!-- Main Content Card -->
  <div class="bg-slate-800/60 backdrop-blur-sm border border-slate-600 rounded-xl shadow-2xl overflow-hidden">
    <!-- Toolbar -->
    <div class="bg-slate-700/50 border-b border-slate-600 p-6">
      <div class="flex items-center justify-between gap-4">
        <div class="flex items-center gap-4 flex-1">
          <!-- Search -->
          <div class="relative flex-1 max-w-md">
            <i class="pi pi-search absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400"></i>
            <input
              #searchInput
              type="text"
              [(ngModel)]="searchValue"
              (input)="onGlobalFilter(dt, $event)"
              placeholder="Search proposals..."
              class="w-full bg-slate-900/50 border border-slate-600 rounded-lg pl-10 pr-4 py-3 text-white placeholder-slate-400 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all duration-200"
            />
          </div>

          <!-- Status Filter -->
          <p-dropdown
            [(ngModel)]="selectedStatus"
            [options]="statusOptions"
            optionLabel="label"
            optionValue="value"
            placeholder="Filter by Status"
            [style]="{ minWidth: '200px' }"
            styleClass="custom-dropdown">
          </p-dropdown>
        </div>

        <!-- Clear Button -->
        <button
          (click)="clear(dt)"
          class="flex items-center gap-2 bg-slate-600 hover:bg-slate-500 text-white px-4 py-2 rounded-lg transition-all duration-200">
          <i class="pi pi-filter-slash text-sm"></i>
          <span>Clear</span>
        </button>
      </div>
    </div>